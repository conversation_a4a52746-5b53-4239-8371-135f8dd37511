import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:city_pickers/city_pickers.dart';

/// 地址选择工具类
class AddressSelector {
  /// 显示地址选择器
  static Future<Map<String, String>?> showAddressPicker(BuildContext context) async {
    try {
      Result? result = await CityPickers.showCityPicker(
        context: context,
        theme: ThemeData(
          // 基础色彩配置
          primarySwatch: Colors.blue,
          primaryColor: const Color(0xFF4285F4),
          
          // AppBar 样式 - 简化配置
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.white,
            elevation: 0,
            centerTitle: true,
            titleTextStyle: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            iconTheme: IconThemeData(
              color: Color(0xFF666666),
              size: 24,
            ),
          ),
          
          // 背景色
          scaffoldBackgroundColor: const Color(0xFFFAFAFA),
          
          // 列表项主题 - 精简配置
          listTileTheme: const ListTileThemeData(
            contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            selectedTileColor: Color(0xFFF0F8FF),
            selectedColor: Color(0xFF4285F4),
            titleTextStyle: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF333333),
            ),
            dense: false,
          ),
          
          // 按钮主题 - 优化性能
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4285F4),
              foregroundColor: Colors.white,
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              minimumSize: const Size(88, 44),
            ),
          ),
          
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF666666),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              minimumSize: const Size(72, 40),
            ),
          ),
          
          // 颜色方案
          colorScheme: const ColorScheme.light(
            primary: Color(0xFF4285F4),
            surface: Colors.white,
            onSurface: Color(0xFF333333),
          ),
        ),
        locationCode: "110000",
      );

      if (result != null) {
        return {
          'province': result.provinceName ?? '',
          'city': result.cityName ?? '',
          'district': result.areaName ?? '',
        };
      }
      return null;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('地址选择失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      return null;
    }
  }
}

class AddressModel {
  final String id;
  final String name;
  final String phone;
  final String province;
  final String city;
  final String district;
  final String detailAddress;
  final bool isDefault;

  AddressModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.province,
    required this.city,
    required this.district,
    required this.detailAddress,
    this.isDefault = false,
  });

  String get fullAddress => '$province$city$district';
}

class AddressManagementPage extends StatefulWidget {
  const AddressManagementPage({super.key});

  @override
  State<AddressManagementPage> createState() => _AddressManagementPageState();
}

class _AddressManagementPageState extends State<AddressManagementPage> {
  List<AddressModel> addresses = [];

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  void _loadAddresses() {
    // TODO: 从后端或本地存储加载地址数据
    setState(() {
      addresses = [
        AddressModel(
          id: '1',
          name: '陈立斌',
          phone: '132****2080',
          province: '广东省',
          city: '广州市',
          district: '海珠区',
          detailAddress: '三七互娱总部大厦xxxxxxxxxxxxxxx',
          isDefault: true,
        ),
        AddressModel(
          id: '2',
          name: '陈立斌',
          phone: '132****2080',
          province: '广东省',
          city: '广州市',
          district: '海珠区',
          detailAddress: '三七互娱总部大厦',
        ),
      ];
    });
  }

  void _navigateToAddAddress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddAddressPage(),
      ),
    ).then((_) => _loadAddresses());
  }

  void _navigateToEditAddress(AddressModel address) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditAddressPage(address: address),
      ),
    ).then((_) => _loadAddresses());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          '地址管理',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            left: BorderSide(
              color: Color(0xFFE5E5E5),
              width: 1,
            ),
          ),
        ),
        child: Column(
          children: [
            // 主要内容区域
            Expanded(
              child: addresses.isEmpty ? _buildEmptyState() : _buildAddressList(),
            ),
            
            // 底部按钮
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 40),
              margin: const EdgeInsets.only(top: 20),
              child: ElevatedButton(
                onPressed: _navigateToAddAddress,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4285F4),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  '新建收货地址',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 地址图标
          SizedBox(
            width: 140,
            height: 140,
            child: ClipOval(
              child: Image.asset(
                'assets/images/address_icon.png',
                width: 120,
                height: 120,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.location_on,
                    size: 60,
                    color: Color(0xFF9E9E9E),
                  );
                },
              ),
            ),
          ),
          // 空状态文本
          const Text(
            '暂无收货地址',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF9E9E9E),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: addresses.length,
      itemBuilder: (context, index) {
        final address = addresses[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: const Color(0xFFE5E5E5),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                address.name,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Text(
                                address.phone,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.black87,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${address.fullAddress}${address.detailAddress}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF666666),
                              height: 1.4,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: () => _navigateToEditAddress(address),
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        child: Image.asset(
                          'assets/images/address_edit_icon.png',
                          width: 16,
                          height: 16,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.edit_outlined,
                              size: 16,
                              color: Color(0xFF666666),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class AddAddressPage extends StatefulWidget {
  const AddAddressPage({super.key});

  @override
  State<AddAddressPage> createState() => _AddAddressPageState();
}

class _AddAddressPageState extends State<AddAddressPage> {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _detailController = TextEditingController();
  final _addressInputController = TextEditingController(); // 新增：地址输入框控制器
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedDistrict = '';

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _detailController.dispose();
    _addressInputController.dispose(); // 新增：释放地址输入框控制器
    super.dispose();
  }

  void _pasteAndParseAddress() async {
    try {
      // 如果输入框为空，则从剪贴板粘贴；如果有内容，则直接识别
      String addressText = _addressInputController.text.trim();
      
      if (addressText.isEmpty) {
        // 从剪贴板获取内容并填充到输入框
        final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
        final clipboardText = clipboardData?.text?.trim() ?? '';
        
        if (clipboardText.isEmpty) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('请在输入框中输入地址信息，或剪贴板为空')),
          );
          return;
        }
        
        // 将剪贴板内容填充到输入框
        _addressInputController.text = clipboardText;
        addressText = clipboardText;
      }
      
      if (!mounted) return;

      final parsedData = _parseAddressText(addressText);
      
      if (parsedData.isNotEmpty) {
        setState(() {
          if (parsedData['name'] != null) {
            _nameController.text = parsedData['name']!;
          }
          if (parsedData['phone'] != null) {
            _phoneController.text = parsedData['phone']!;
          }
          if (parsedData['province'] != null) {
            _selectedProvince = parsedData['province']!;
          }
          if (parsedData['city'] != null) {
            _selectedCity = parsedData['city']!;
          }
          if (parsedData['district'] != null) {
            _selectedDistrict = parsedData['district']!;
          }
          if (parsedData['detailAddress'] != null) {
            _detailController.text = parsedData['detailAddress']!;
          }
        });
        
        // 识别成功后清空输入框
        _addressInputController.clear();
        
        if (!mounted) return;
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('未能识别有效地址信息')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('识别失败')),
      );
    }
  }

  Map<String, String> _parseAddressText(String text) {
    final result = <String, String>{};
    
    // 清理输入文本，统一换行符并移除多余空格
    String cleanText = text.trim();
    cleanText = cleanText.replaceAll(RegExp(r'\r\n|\r|\n'), ' '); // 将换行符替换为空格
    cleanText = cleanText.replaceAll(RegExp(r'\s+'), ' '); // 合并多个空格
    
    // 先尝试识别带标签的格式（姓名:、电话:、地址: 等）
    _parseTaggedFormat(cleanText, result);
    
    // 清理已识别的标签格式内容
    String remainingText = cleanText;
    
    // 移除标签格式的内容
    remainingText = remainingText.replaceAll(RegExp(r'(姓名|联系人|收货人|收件人)[:：]\s*[\u4e00-\u9fa5]{2,10}'), '');
    remainingText = remainingText.replaceAll(RegExp(r'(电话|手机|手机号码?)[:：]\s*1[3-9]\d{9}'), '');
    remainingText = remainingText.replaceAll(RegExp(r'(地址|收货地址|详细地址|所在地区)[:：]\s*'), '');
    
    // 清理多余的前缀和空格
    remainingText = remainingText.replaceAll(RegExp(r'(地址[:：]?\s*|收货地址[:：]?\s*|详细地址[:：]?\s*)'), '');
    remainingText = remainingText.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // 如果标签格式没有识别到手机号，尝试从剩余文本识别
    if (result['phone'] == null) {
      final phoneRegex = RegExp(r'1[3-9]\d{9}');
      final phoneMatch = phoneRegex.firstMatch(remainingText);
      if (phoneMatch != null) {
        result['phone'] = phoneMatch.group(0)!;
        remainingText = remainingText.replaceAll(phoneMatch.group(0)!, ' ').trim();
      }
    }
    
    // 识别省份 - 优先识别直辖市和特别行政区
    if (result['province'] == null) {
      final provinceRegex = RegExp(r'(北京市|天津市|上海市|重庆市|香港特别行政区|澳门特别行政区|台湾省|[\u4e00-\u9fa5]{2,8}省)');
      final provinceMatch = provinceRegex.firstMatch(remainingText);
      if (provinceMatch != null) {
        result['province'] = provinceMatch.group(0)!;
        
        // 如果是直辖市，同时设为市
        final province = result['province']!;
        if (['北京市', '天津市', '上海市', '重庆市'].contains(province)) {
          result['city'] = province;
        }
        
        remainingText = remainingText.replaceAll(result['province']!, ' ').trim();
      }
    }
    
    // 识别城市 - 仅在非直辖市情况下识别
    if (result['city'] == null) {
      final cityRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}市');
      final cityMatch = cityRegex.firstMatch(remainingText);
      if (cityMatch != null) {
        result['city'] = cityMatch.group(0)!;
        remainingText = remainingText.replaceAll(cityMatch.group(0)!, ' ').trim();
      }
    }
    
    // 识别区县
    if (result['district'] == null) {
      final districtRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}[区县]');
      final districtMatch = districtRegex.firstMatch(remainingText);
      if (districtMatch != null) {
        result['district'] = districtMatch.group(0)!;
        remainingText = remainingText.replaceAll(districtMatch.group(0)!, ' ').trim();
      }
    }
    
    // 如果标签格式没有识别到姓名，尝试从剩余文本识别
    if (result['name'] == null) {
      // 分析原始文本结构，尝试找到姓名
      final phoneRegex = RegExp(r'1[3-9]\d{9}');
      final phoneInOriginal = phoneRegex.firstMatch(cleanText);
      if (phoneInOriginal != null) {
        String beforePhone = cleanText.substring(0, phoneInOriginal.start).trim();
        
        // 从开头部分寻找可能的姓名
        final nameRegex = RegExp(r'[\u4e00-\u9fa5]{2,4}(?=\s|$|[0-9])');
        final nameMatch = nameRegex.firstMatch(beforePhone);
        if (nameMatch != null) {
          final potentialName = nameMatch.group(0)!;
          
          // 检查是否为合理的姓名（不是地名）
          if (_isValidName(potentialName)) {
            result['name'] = potentialName;
            remainingText = remainingText.replaceFirst(potentialName, ' ').trim();
          }
        }
      }
      
      // 如果还没找到姓名，从剩余文本中尝试识别
      if (result['name'] == null && remainingText.isNotEmpty) {
        final parts = remainingText.split(RegExp(r'[\s,，]+'));
        for (final part in parts) {
          final nameRegex = RegExp(r'^[\u4e00-\u9fa5]{2,4}$');
          if (nameRegex.hasMatch(part) && _isValidName(part)) {
            result['name'] = part;
            remainingText = remainingText.replaceFirst(part, ' ').trim();
            break;
          }
        }
      }
    }
    
    // 最后处理详细地址
    remainingText = remainingText.replaceAll(RegExp(r'\s+'), ' ').trim();
    remainingText = remainingText.replaceAll(RegExp(r'^[,，\s]+|[,，\s]+$'), '');
    
    // 进一步清理详细地址中可能残留的无用信息
    if (remainingText.isNotEmpty) {
      // 移除可能的重复地名片段
      for (final key in ['province', 'city', 'district', 'name']) {
        if (result[key] != null) {
          remainingText = remainingText.replaceAll(result[key]!, '');
        }
      }
      
      remainingText = remainingText.replaceAll(RegExp(r'\s+'), ' ').trim();
      remainingText = remainingText.replaceAll(RegExp(r'^[,，\s]+|[,，\s]+$'), '');
      
      if (remainingText.isNotEmpty && remainingText.length > 1) {
        result['detailAddress'] = remainingText;
      }
    }
    
    // 如果从标签格式中已经获得了详细地址，需要进一步清理
    if (result['detailAddress'] != null) {
      String detailAddress = result['detailAddress']!;
      
      // 移除详细地址中的省市区重复信息
      for (final key in ['province', 'city', 'district']) {
        if (result[key] != null) {
          detailAddress = detailAddress.replaceAll(result[key]!, '');
        }
      }
      
      detailAddress = detailAddress.replaceAll(RegExp(r'\s+'), ' ').trim();
      detailAddress = detailAddress.replaceAll(RegExp(r'^[,，\s]+|[,，\s]+$'), '');
      
      if (detailAddress.isNotEmpty && detailAddress.length > 1) {
        result['detailAddress'] = detailAddress;
      } else {
        result.remove('detailAddress');
      }
    }
    
    return result;
  }

  // 解析带标签的格式（姓名:、电话:、地址: 等）
  void _parseTaggedFormat(String text, Map<String, String> result) {
    // 识别姓名标签
    final nameTagRegex = RegExp(r'(姓名|联系人|收货人|收件人)[:：]\s*([\u4e00-\u9fa5]{2,10})');
    final nameTagMatch = nameTagRegex.firstMatch(text);
    if (nameTagMatch != null) {
      final name = nameTagMatch.group(2)!.trim();
      if (_isValidName(name)) {
        result['name'] = name;
      }
    }
    
    // 识别电话标签
    final phoneTagRegex = RegExp(r'(电话|手机|手机号码?)[:：]\s*(1[3-9]\d{9})');
    final phoneTagMatch = phoneTagRegex.firstMatch(text);
    if (phoneTagMatch != null) {
      result['phone'] = phoneTagMatch.group(2)!;
    }
    
    // 优先识别"所在地区"标签（这通常包含省市区信息）
    final regionTagRegex = RegExp(r'所在地区[:：]\s*(.+?)(?=(姓名|联系人|收货人|收件人|电话|手机|详细地址)[:：]|$)');
    final regionTagMatch = regionTagRegex.firstMatch(text);
    if (regionTagMatch != null) {
      final regionPart = regionTagMatch.group(1)!.trim();
      _parseAddressPart(regionPart, result);
    }
    
    // 识别详细地址标签
    final detailAddressTagRegex = RegExp(r'详细地址[:：]\s*(.+?)(?=(姓名|联系人|收货人|收件人|电话|手机|所在地区)[:：]|$)');
    final detailAddressTagMatch = detailAddressTagRegex.firstMatch(text);
    if (detailAddressTagMatch != null) {
      final detailAddress = detailAddressTagMatch.group(1)!.trim();
      if (detailAddress.isNotEmpty && detailAddress.length > 1) {
        result['detailAddress'] = detailAddress;
      }
    }
    
    // 如果没有"所在地区"标签，尝试识别普通的地址标签
    if (result['province'] == null && result['city'] == null && result['district'] == null) {
      final addressTagRegex = RegExp(r'(地址|收货地址)[:：]\s*(.+?)(?=(姓名|联系人|收货人|收件人|电话|手机|详细地址)[:：]|$)');
      final addressTagMatch = addressTagRegex.firstMatch(text);
      if (addressTagMatch != null) {
        final addressPart = addressTagMatch.group(2)!.trim();
        _parseAddressPart(addressPart, result);
      }
    }
  }

  // 解析地址部分的省市区
  void _parseAddressPart(String addressText, Map<String, String> result) {
    String cleanAddress = addressText.trim();
    
    // 识别省份
    final provinceRegex = RegExp(r'(北京市|天津市|上海市|重庆市|香港特别行政区|澳门特别行政区|台湾省|[\u4e00-\u9fa5]{2,8}省)');
    final provinceMatch = provinceRegex.firstMatch(cleanAddress);
    if (provinceMatch != null) {
      result['province'] = provinceMatch.group(0)!;
      
      // 如果是直辖市，同时设为市
      final province = result['province']!;
      if (['北京市', '天津市', '上海市', '重庆市'].contains(province)) {
        result['city'] = province;
      }
      
      cleanAddress = cleanAddress.replaceAll(result['province']!, ' ').trim();
    }
    
    // 识别城市（非直辖市）
    if (result['city'] == null) {
      final cityRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}市');
      final cityMatch = cityRegex.firstMatch(cleanAddress);
      if (cityMatch != null) {
        result['city'] = cityMatch.group(0)!;
        cleanAddress = cleanAddress.replaceAll(cityMatch.group(0)!, ' ').trim();
      }
    }
    
    // 识别区县
    final districtRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}[区县]');
    final districtMatch = districtRegex.firstMatch(cleanAddress);
    if (districtMatch != null) {
      result['district'] = districtMatch.group(0)!;
      cleanAddress = cleanAddress.replaceAll(districtMatch.group(0)!, ' ').trim();
    }
    
    // 识别街道/镇（如果存在）- 将其包含在详细地址的开头
    final streetRegex = RegExp(r'[\u4e00-\u9fa5]{2,8}(街道|镇|乡|社区)');
    final streetMatch = streetRegex.firstMatch(cleanAddress);
    String streetInfo = '';
    if (streetMatch != null) {
      streetInfo = streetMatch.group(0)!;
      cleanAddress = cleanAddress.replaceAll(streetInfo, ' ').trim();
    }
    
    // 剩余部分作为详细地址，街道信息作为详细地址的前缀
    cleanAddress = cleanAddress.replaceAll(RegExp(r'\s+'), ' ').trim();
    if (cleanAddress.isNotEmpty || streetInfo.isNotEmpty) {
      String detailAddress = '';
      if (streetInfo.isNotEmpty) {
        detailAddress = streetInfo;
        if (cleanAddress.isNotEmpty) {
          detailAddress = '$detailAddress $cleanAddress';
        }
      } else {
        detailAddress = cleanAddress;
      }
      
      if (detailAddress.isNotEmpty && detailAddress.length > 1) {
        // 如果已经有详细地址（来自"详细地址"标签），将街道信息放在前面
        if (result['detailAddress'] != null) {
          if (streetInfo.isNotEmpty) {
            result['detailAddress'] = '$streetInfo ${result['detailAddress']!}';
          }
        } else {
          result['detailAddress'] = detailAddress;
        }
      }
    }
  }

  // 验证是否为有效姓名
  bool _isValidName(String name) {
    // 排除常见的地名词汇和关键词
    final excludeWords = [
      '东路', '西路', '南路', '北路', '中路', '大道', '大街', '小区', '社区', '村委', '街道', '镇区',
      '开发', '工业', '科技', '商业', '住宅', '花园', '公园', '广场', '中心', '大厦', '大楼', '写字楼',
      '芳园', '天河', '海珠', '越秀', '荔湾', '白云', '黄埔', '番禺', '花都', '南沙', '从化', '增城',
      '三七', '互娱', '公司', '有限', '集团', '企业', '单位', '部门'
    ];
    
    for (final exclude in excludeWords) {
      if (name.contains(exclude) || exclude.contains(name) || 
          name.endsWith('路') || name.endsWith('街') || name.endsWith('巷') ||
          name.endsWith('区') || name.endsWith('县') || name.endsWith('市')) {
        return false;
      }
    }
    
    return true;
  }

  void _selectLocation() async {
    final result = await AddressSelector.showAddressPicker(context);
    if (result != null && mounted) {
      setState(() {
        _selectedProvince = result['province'] ?? '';
        _selectedCity = result['city'] ?? '';
        _selectedDistrict = result['district'] ?? '';
      });
    }
  }

  void _saveAddress() {
    if (_nameController.text.isEmpty || 
        _phoneController.text.isEmpty ||
        _selectedProvince.isEmpty ||
        _detailController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请填写完整信息')),
      );
      return;
    }

    // TODO: 保存地址到后端或本地存储
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('地址保存成功')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 防止键盘弹出时调整布局
      appBar: AppBar(
        title: const Text(
          '新建地址',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildInputField('联系人', '请填写收货人姓名', _nameController),
                  _buildPhoneField(),
                  _buildLocationField(),
                  const SizedBox(height: 15),
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.symmetric(horizontal: 0),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 整个区域为输入框
                        TextField(
                          controller: _addressInputController,
                          maxLines: null, // 自适应行数
                          minLines: 3,    // 最少3行
                          decoration: const InputDecoration(
                            hintText: '试试粘贴收件人姓名、联系方式、地址等信息，可快速识别',
                            hintStyle: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF999999),
                              height: 1.4,
                            ),
                            border: InputBorder.none, // 去掉边框，保持原有外观
                            contentPadding: EdgeInsets.zero, // 去掉内边距
                            isDense: true, // 紧凑布局
                          ),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF333333),
                            height: 1.4,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Align(
                          alignment: Alignment.centerRight,
                          child: GestureDetector(
                            onTap: _pasteAndParseAddress,
                            child: const Text(
                              '粘贴并识别地址',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF4285F4),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 40),
            child: ElevatedButton(
              onPressed: _saveAddress,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: const Text(
                '保存',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField(String label, String hint, TextEditingController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: const TextStyle(
                  color: Color(0xFFCCCCCC),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneField() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '手机号码',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                hintText: '请填写收货人手机号',
                hintStyle: TextStyle(
                  color: Color(0xFFCCCCCC),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          const Text(
            '+86',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationField() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '选择地址',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: _selectLocation,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            _selectedProvince.isEmpty
                                ? '请选择所在地区'
                                : '$_selectedProvince$_selectedCity$_selectedDistrict',
                            style: TextStyle(
                              fontSize: 14,
                              color: _selectedProvince.isEmpty
                                  ? const Color(0xFFCCCCCC)
                                  : Colors.black87,
                            ),
                          ),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Color(0xFF999999),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '详细地址',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _detailController,
                  decoration: const InputDecoration(
                    hintText: '请填写街道门牌等详细信息',
                    hintStyle: TextStyle(
                      color: Color(0xFFCCCCCC),
                      fontSize: 14,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class EditAddressPage extends StatefulWidget {
  final AddressModel address;

  const EditAddressPage({super.key, required this.address});

  @override
  State<EditAddressPage> createState() => _EditAddressPageState();
}

class _EditAddressPageState extends State<EditAddressPage> {
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _detailController;
  late String _selectedProvince;
  late String _selectedCity;
  late String _selectedDistrict;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.address.name);
    _phoneController = TextEditingController(text: widget.address.phone);
    _detailController = TextEditingController(text: widget.address.detailAddress);
    _selectedProvince = widget.address.province;
    _selectedCity = widget.address.city;
    _selectedDistrict = widget.address.district;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _detailController.dispose();
    super.dispose();
  }

  void _selectLocation() async {
    final result = await AddressSelector.showAddressPicker(context);
    if (result != null && mounted) {
      setState(() {
        _selectedProvince = result['province'] ?? '';
        _selectedCity = result['city'] ?? '';
        _selectedDistrict = result['district'] ?? '';
      });
    }
  }

  void _saveAddress() {
    if (_nameController.text.isEmpty || 
        _phoneController.text.isEmpty ||
        _selectedProvince.isEmpty ||
        _detailController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请填写完整信息')),
      );
      return;
    }

    // TODO: 更新地址到后端或本地存储
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('地址更新成功')),
    );
  }

  void _deleteAddress() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除地址'),
        content: const Text('确定要删除这个地址吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('地址删除成功')),
              );
            },
            child: const Text(
              '删除',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 防止键盘弹出时调整布局
      appBar: AppBar(
        title: const Text(
          '修改地址',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildEditInputField('联系人', _nameController.text, _nameController),
                  _buildEditPhoneField(),
                  _buildEditLocationField(),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 40),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _deleteAddress,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.black87,
                      side: const BorderSide(color: Color(0xFFE5E5E5)),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '删除',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveAddress,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4285F4),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: const Text(
                      '保存',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditInputField(String label, String value, TextEditingController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: value,
                hintStyle: const TextStyle(
                  color: Color(0xFFCCCCCC),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditPhoneField() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5)),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '手机号码',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          const Text(
            '+86',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditLocationField() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '选择地址',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: _selectLocation,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            '$_selectedProvince$_selectedCity$_selectedDistrict',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Color(0xFF999999),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFE5E5E5)),
            ),
          ),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  '详细地址',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _detailController,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
