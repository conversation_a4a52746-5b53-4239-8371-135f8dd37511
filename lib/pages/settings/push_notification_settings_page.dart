import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../utils/permission_utils.dart';
import 'package:permission_handler/permission_handler.dart';

class PushNotificationSettingsPage extends StatefulWidget {
  const PushNotificationSettingsPage({super.key});

  @override
  State<PushNotificationSettingsPage> createState() => _PushNotificationSettingsPageState();
}

class _PushNotificationSettingsPageState extends State<PushNotificationSettingsPage> with WidgetsBindingObserver {
  bool _isNotificationEnabled = false;
  bool _isPermissionGranted = false;
  bool _isLoading = true;

  static const String _notificationEnabledKey = 'push_notification_enabled';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkPermissionAndLoadSettings();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // 当应用从后台返回前台时，延迟一下再重新检查权限状态
      // 这样可以确保系统设置页面完全关闭后再检查
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _checkPermissionAndLoadSettings();
        }
      });
    }
  }

  /// 检查通知权限并加载设置
  Future<void> _checkPermissionAndLoadSettings() async {
    try {
      // 检查通知权限状态
      final isGranted = await PermissionUtils.isPermissionGranted(PermissionType.notification);
      final isPermanentlyDenied = await PermissionUtils.isPermissionPermanentlyDenied(PermissionType.notification);

      // 确保widget还在树中再调用setState
      if (mounted) {
        setState(() {
          _isPermissionGranted = isGranted;
          _isLoading = false;
        });

        // 加载设置
        await _loadSettings();
      }
    } catch (e) {
      print('检查权限状态时出错: $e');
      // 如果出错，确保widget还在树中再调用setState
      if (mounted) {
        setState(() {
          _isPermissionGranted = false;
          _isLoading = false;
        });
      }
    }
  }

  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (mounted) {
        setState(() {
          _isNotificationEnabled = prefs.getBool(_notificationEnabledKey) ?? false;
        });
      }
    } catch (e) {
      print('加载设置时出错: $e');
    }
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationEnabledKey, _isNotificationEnabled);
  }

  /// 请求通知权限
  Future<void> _requestNotificationPermission() async {
    try {
      final isGranted = await PermissionUtils.requestNotificationPermission(context);
      if (mounted) {
        setState(() {
          _isPermissionGranted = isGranted;
        });
      }

      if (!isGranted) {
        // 权限被拒绝，PermissionUtils会自动处理弹窗
      }
    } catch (e) {
      print('请求通知权限时出错: $e');
      if (mounted) {
        setState(() {
          _isPermissionGranted = false;
        });
      }
    }
  }

  /// 显示权限说明对话框
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要通知权限'),
          content: const Text('为了及时接收推送通知，请在设置中开启通知权限。'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                '取消',
                style: TextStyle(color: Color(0xFF999999)),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                openAppSettings();
              },
              child: const Text(
                '去设置',
                style: TextStyle(color: Color(0xFF007AFF)),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 处理通知开关变化
  Future<void> _handleNotificationToggle() async {
    if (!_isPermissionGranted) {
      // 如果没有权限，请求权限
      await _requestNotificationPermission();
      if (_isPermissionGranted) {
        // 权限获取成功，保存设置
        setState(() {
          _isNotificationEnabled = true;
        });
        await _saveSettings();
      }
    } else {
      // 已有权限，直接跳转到系统设置页面让用户手动关闭
      openAppSettings();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '推送设置',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Column(
              children: [
                Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      _buildNotificationItem(
                        title: '接受推送通知',
                        subtitle: _getNotificationSubtitle(),
                        enabled: _isNotificationEnabled && _isPermissionGranted,
                        onTap: _handleNotificationToggle,
                        showArrow: !_isPermissionGranted,
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  String _getNotificationSubtitle() {
    if (!_isPermissionGranted) {
      return '已关闭，点击开启';
    } else {
      return '已开启';
    }
  }

  Widget _buildNotificationItem({
    required String title,
    required String subtitle,
    required bool enabled,
    VoidCallback? onTap,
    bool showArrow = true,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            Row(
              children: [
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF999999),
                  ),
                ),
                if (showArrow) ...[
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Color(0xFFCCCCCC),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.only(left: 16),
      height: 1,
      color: const Color(0xFFF0F0F0),
    );
  }
}