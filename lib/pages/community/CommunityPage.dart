import 'dart:io';

import 'package:dlyz_flutter/common/dl_color.dart';
import 'package:dlyz_flutter/config/app_config.dart';
import 'package:dlyz_flutter/net/config/http_base_config.dart';
import 'package:dlyz_flutter/pages/voucher/voucher_page.dart';
import 'package:dlyz_flutter/providers/game_circle_provider.dart';
import 'package:dlyz_flutter/providers/game_circle_config_provider.dart';
import 'package:dlyz_flutter/utils/device_info_util.dart';
import 'package:dlyz_flutter/utils/enhanced_device_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'dart:convert';
import '../../components/game_character_binding_dialog.dart';
import '../../components/cache_image.dart';
import '../../info/forum_info.dart';
import '../../model/game_role.dart';
import '../../providers/function_area_provider.dart';
import '../../providers/user_provider.dart';
import '../../providers/role_provider.dart';
import '../../services/bind_account_service.dart';
import '../../webview/web_router.dart';
import '../gift/gift_center_page.dart';
import '../bind/character_bind_page.dart';
import '../bind/switch_bind_character_page.dart';
import 'switch_bind_role.dart';
import 'forum_post_list_page.dart';
import 'forum_post_waterfall_list_page.dart';
import '../../net/api/forum_service.dart';
import '../../net/api/init_service.dart';
import '../../net/api/game_circle_service.dart';
import '../../model/forum_category.dart';
import '../../model/act_module.dart';
import '../../model/forum_post_list.dart';
import '../../model/game_circle.dart';
import '../../model/function_area.dart';
import '../../components/game_circle_selection_dialog.dart';
import 'community_detail_page.dart';
import '../../utils/activity_link_handler.dart';
import 'collection_page.dart';
import 'post_search_page.dart';
import 'topic_page.dart';
import '../update/update_dialog.dart';
import '../../utils/log_util.dart';
import '../../webview/webview_dialog.dart';



class CommunityPage extends StatefulWidget {
  /// 是否显示游戏画廊模式
  /// true: 显示游戏画廊页面 (forum_post_waterfall_list_page.dart)
  /// false: 显示论坛帖子列表 (forum_post_list_page.dart)
  final bool showGameGallery;

  const CommunityPage({super.key, this.showGameGallery = false});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage>
    with TickerProviderStateMixin {
  int _selectedTabIndex = 0;
  List<ForumCategory> _categories = [];
  List<ActModule> _activityModules = [];
  List<FunctionArea> _functionAreas = [];
  bool _isLoading = true;
  String? _error;
  TabController? _tabController;

  // 预缓存的指示器图片
  final AssetImage _tabIndicatorAsset = const AssetImage(
    'assets/images/tab_indicator.png',
  );

  // 功能图标滑动控制器和进度
  final ScrollController _functionScrollController = ScrollController();
  final ValueNotifier<double> _scrollProgressNotifier = ValueNotifier<double>(
    0.0,
  );

  // 缓存已构建的tab页面
  final Map<int, Widget> _cachedTabPages = {};

  // 缓存已构建的活动图标
  final Map<String, Widget> _cachedActivityIcons = {};

  // 更新类型常量
  static const String TYPE_NORMAL = "1"; // 正常，不需要更新
  static const String TYPE_UPDATE = "2"; // 普通更新
  static const String TYPE_FORCE = "3"; // 强制更新

  // 用于控制是否需要显示更新弹窗
  bool _showUpdateDialogFlag = false;
  String _updateContent = '';
  String _apkUrl = '';
  String _version = '';
  bool _isForceUpdate = false;


  @override
  void initState() {
    super.initState();
    // 设置沉浸式状态栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    _initializeForumAndLoadData();
    _initScrollListener();

    // 检查是否需要强更
    _checkForUpdate();

    // 加载圈子配置
    _loadCircleConfig();

    // 加载角色绑定数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<RoleProvider>(context, listen: false).loadBoundRoles(context);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 预加载Tab指示器图片，保证首次展示时即可绘制
    precacheImage(_tabIndicatorAsset, context);
  }

  /// 初始化论坛配置并加载数据
  Future<void> _initializeForumAndLoadData() async {
    try {
      LogUtil.d('开始初始化论坛配置...', tag: 'CommunityPage');

      // 1. 初始化ForumInfo
      final forumInfo = ForumInfo();
      var configResult = null;
      if (forumInfo.isInitialized) {
        configResult = await forumInfo.refresh(context);
      } else {
        configResult = await forumInfo.initialize(context);
      }

      if (configResult) {
        LogUtil.d('论坛配置初始化成功', tag: 'CommunityPage');

        // 2. 论坛配置初始化成功后，再加载分类和活动模块
        await _loadCategories();
        _loadActivityModules();
        _loadFunctionAreas();
      } else {
        LogUtil.w('论坛配置初始化失败', tag: 'CommunityPage');
        // 即使初始化失败，也尝试加载分类（可能使用默认配置）
        await _loadCategories();
        _loadActivityModules();
        _loadFunctionAreas();
      }
    } catch (e) {
      LogUtil.e('初始化论坛配置时发生异常', error: e, tag: 'CommunityPage');
      // 发生异常时也尝试加载分类
      await _loadCategories();
      _loadActivityModules();
      _loadFunctionAreas();
    }
  }

  void _loadActivityModules() {
    // 从ForumInfo获取活动模块数据
    final forumInfo = ForumInfo();
    if (forumInfo.isInitialized && forumInfo.forumConfig != null) {
      final modules = forumInfo.actModules;
      LogUtil.d(
        'ForumInfo actModules count: ${modules.length}',
        tag: 'CommunityPage',
      );
      if (mounted) {
        setState(() {
          _activityModules = modules;
        });
      }
    } else {
      LogUtil.d('ForumInfo not initialized yet', tag: 'CommunityPage');
    }
  }

  /// 加载功能区列表
  Future<void> _loadFunctionAreas() async {
    try {
      LogUtil.d('开始加载功能区列表...', tag: 'CommunityPage');

      // 从GameCircleProvider获取当前选中的圈子信息
      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );
      final functionAreaProvider = Provider.of<FunctionAreaProvider>(
          context,
          listen: false,
      );

      if (!gameCircleProvider.hasSelectedGameCircle ||
          gameCircleProvider.selectedGameCircle == null) {
        LogUtil.w('没有选中的游戏圈子，无法加载功能区列表', tag: 'CommunityPage');
        return;
      }

      final selectedCircle = gameCircleProvider.selectedGameCircle!;
      final tgid = selectedCircle.tgid;
      final circleId = selectedCircle.circleId;

      LogUtil.d(
        '使用圈子信息加载功能区: tgid=$tgid, circleId=$circleId',
        tag: 'CommunityPage',
      );
      functionAreaProvider.setLoading(true);
      GameCircleService.context = context;

      // 1. 先加载功能区列表
      final response = await GameCircleService.getFunctionAreaList(
        context: context,
        tgid: tgid,
        circleId: circleId,
      );

      LogUtil.d(
        '接口响应: success=${response.success}, data=${response.data}',
        tag: 'CommunityPage',
      );

      if (response.data != null) {
        LogUtil.d(
          '功能区列表加载成功，获取到 ${response.data!.length} 个功能区',
          tag: 'CommunityPage',
        );
        LogUtil.d('功能区数据: ${response.data!}', tag: 'CommunityPage');


        // 保存到Provider
        functionAreaProvider.updateFunctionAreas(response.data ?? []);

        // 2. 加载功能区状态（角标信息）
        await _loadFunctionAreaStatus(tgid, circleId, response.data!);

        if (mounted) {
          setState(() {
            _functionAreas = response.data!;
          });
        }
      } else {
        LogUtil.w(
          '功能区列表加载失败: success=${response.success}, msg=${response.message}',
          tag: 'CommunityPage',
        );
        functionAreaProvider.setError(response.message);
      }
    } catch (e) {
      LogUtil.e('加载功能区列表异常: $e', tag: 'CommunityPage');
      final functionAreaProvider = Provider.of<FunctionAreaProvider>(context, listen: false);
      functionAreaProvider.setError('加载功能区列表异常: $e');
    }
  }

  /// 加载功能区状态（角标信息）
  Future<void> _loadFunctionAreaStatus(
    String tgid,
    String circleId,
    List<FunctionArea> functionAreas,
  ) async {
    try {
      LogUtil.d('开始加载功能区状态...', tag: 'CommunityPage');

      final statusResponse = await GameCircleService.getFunctionAreaStatus(
        context: context,
        tgid: tgid,
        circleId: circleId,
      );

      if (statusResponse.data != null) {
        LogUtil.d(
          '功能区状态加载成功，获取到 ${statusResponse.data!.length} 个状态',
          tag: 'CommunityPage',
        );

        // 将状态信息映射到功能区列表
        for (final status in statusResponse.data!) {
          final functionArea = functionAreas.firstWhere(
            (area) => area.areaModule == status.areaModule,
            orElse: () => throw StateError('未找到对应的功能区'),
          );
          if (functionAreas.contains(functionArea)) {
            functionArea.badgeUrl = status.badgeUrl;
          }
        }

        LogUtil.d('功能区状态映射完成', tag: 'CommunityPage');
      } else {
        LogUtil.w('功能区状态加载失败: ${statusResponse.message}', tag: 'CommunityPage');
      }
    } catch (e) {
      LogUtil.e('加载功能区状态异常: $e', tag: 'CommunityPage');
    }
  }

  /// 检查是否需要强更
  Future<void> _checkForUpdate() async {
    try {
      LogUtil.d('开始检查强更...');
      final mResponse = await InitService.mActivate(context: context);

      if (mResponse.success && mResponse.data != null) {
        LogUtil.d('M层激活成功，检查更新配置');
        await _checkUpdateConfig(mResponse.data);
      } else {
        LogUtil.d('M层激活失败: ${mResponse.message}');
      }
    } catch (e) {
      LogUtil.e('检查强更失败: $e');
    }
  }

  /// 检查更新配置
  Future<void> _checkUpdateConfig(dynamic data) async {
    try {
      // 检查是否有更新类型字段
      if (data.utype != null && data.utype!.isNotEmpty) {
        final updateType = data.utype!;
        final apkUrl = data.uurl ?? '';
        final updateContent = data.uct ?? '';
        const version = "1.0";

        LogUtil.d('检查更新配置: updateType=$updateType, apkUrl=$apkUrl');

        if (updateType == TYPE_NORMAL) {
          // 正常，不需要更新
          LogUtil.d('当前版本正常，无需更新');
        } else if (updateType == TYPE_UPDATE) {
          // 普通更新
          LogUtil.d('发现可用更新，设置更新弹窗标志');
          _showUpdateDialogFlag = true;
          _updateContent = updateContent;
          _apkUrl = apkUrl;
          _version = version;
          _isForceUpdate = false;

          // 延迟显示弹窗，确保页面完全加载
          Future.delayed(const Duration(milliseconds: 1000), () {
            if (mounted && _showUpdateDialogFlag) {
              _showUpdateDialog(
                _isForceUpdate,
                _updateContent,
                _apkUrl,
                _version,
              );
            }
          });
        } else if (updateType == TYPE_FORCE) {
          // 强制更新
          LogUtil.d('发现强制更新，设置强制更新弹窗标志');
          _showUpdateDialogFlag = true;
          _updateContent = updateContent;
          _apkUrl = apkUrl;
          _version = version;
          _isForceUpdate = true;

          // 强制更新立即显示弹窗
          if (mounted) {
            _showUpdateDialog(
              _isForceUpdate,
              _updateContent,
              _apkUrl,
              _version,
            );
          }
        }
      }
    } catch (e) {
      LogUtil.e('检查更新配置失败: $e');
    }
  }

  /// 加载圈子配置
  Future<void> _loadCircleConfig() async {
    try {
      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );
      final configProvider = Provider.of<GameCircleConfigProvider>(
        context,
        listen: false,
      );

      // 确保GameCircleProvider已初始化
      if (!gameCircleProvider.isLoaded) {
        await gameCircleProvider.initialize();
      }

      // 如果有选中的圈子，加载其配置
      if (gameCircleProvider.hasSelectedGameCircle &&
          gameCircleProvider.selectedGameCircle != null) {
        final circle = gameCircleProvider.selectedGameCircle!;

        // 使用圈子中的tgid
        await configProvider.loadCircleConfig(
          circleId: circle.circleId,
          tgid: circle.tgid,
          context: context,
        );

        LogUtil.d('圈子配置加载完成: ${configProvider.circleConfig}');
      }
    } catch (e) {
      LogUtil.e('加载圈子配置失败: $e');
    }
  }


  /// 处理切换游戏圈子图标点击事件
  Future<void> _handleSwitchIconClick() async {
    try {
      LogUtil.d('开始请求游戏圈子列表...');

      // 调用游戏圈子列表接口
      final response = await GameCircleService.getGameCircleList(
        context: context,
      );

      if (response.success && response.data != null) {
        final gameCircles = response.data!.circles;
        LogUtil.d('获取到 ${gameCircles.length} 个游戏圈子');

        // 显示游戏圈子选择弹窗
        if (mounted) {
          GameCircleSelectionDialog.show(
            context: context,
            title: '请选择游戏圈',
            gameCircles: gameCircles,
            onCircleSelected: _onGameCircleSelected,
          );
        }
      } else {
        LogUtil.e('获取游戏圈子列表失败: ${response.message}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message ?? '获取游戏圈子列表失败'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      LogUtil.e('请求游戏圈子列表异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('网络异常: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 处理游戏圈子选择
  Future<void> _onGameCircleSelected(GameCircle selectedCircle) async {
    try {
      LogUtil.d('选择了游戏圈: ${selectedCircle.displayName}');

      // 1. 保存选择的游戏圈到Provider
      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );
      final functionAreaProvider = Provider.of<FunctionAreaProvider>(
          context,
          listen: false,
      );

      await gameCircleProvider.selectGameCircle(
        selectedCircle.circleId,
        selectedCircle.displayName,
        selectedCircle,
      );

      // 清除功能区数据，因为圈子已经改变
      functionAreaProvider.clearData();

      // 2. 重新加载圈子配置
      await _loadCircleConfig();

      // 3. 重新初始化论坛配置和数据（因为tgid已经改变）
      LogUtil.d('游戏圈子切换后，重新初始化论坛配置...', tag: 'CommunityPage');

      // 设置加载状态
      if (mounted) {
        setState(() {
          _isLoading = true;
          _error = null;
          _categories = [];
          _activityModules = [];
          _functionAreas = [];
          // 清除tab缓存，强制刷新
          _clearTabCache();
        });
      }

      // 重新初始化论坛配置和加载数据
      await _initializeForumAndLoadData();

      // 重新加载角色绑定数据
      await Provider.of<RoleProvider>(context, listen: false).loadBoundRoles(context);

      LogUtil.d('游戏圈子切换完成，论坛配置和页面已刷新');
    } catch (e) {
      LogUtil.e('切换游戏圈子失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('切换游戏圈子失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 显示更新弹窗
  void _showUpdateDialog(
    bool isForce,
    String updateContent,
    String apkUrl,
    String version,
  ) {
    if (!mounted) {
      LogUtil.e('页面已销毁，无法显示更新弹窗');
      return;
    }

    LogUtil.d('准备显示更新弹窗: isForce=$isForce, apkUrl=$apkUrl, version=$version');
    LogUtil.d('更新内容: $updateContent');

    try {
      // 使用 VersionUpdateDialog 组件
      VersionUpdateHelper.showVersionUpdateDialog(
        context,
        exitAppOnClose: isForce, // 强制更新时关闭弹窗会退出应用
        downloadUrl: apkUrl,
        message:
            updateContent.isNotEmpty ? updateContent : '发现新版本，建议立即更新以获得更好的体验。',
        version: version,
      );

      LogUtil.d('更新弹窗显示调用完成');
    } catch (e) {
      LogUtil.e('显示更新弹窗失败: $e');
    }
  }

  void _initScrollListener() {
    _functionScrollController.addListener(() {
      if (_functionScrollController.hasClients) {
        final maxScrollExtent =
            _functionScrollController.position.maxScrollExtent;
        final currentScroll = _functionScrollController.offset;

        if (maxScrollExtent > 0) {
          final newProgress = (currentScroll / maxScrollExtent).clamp(0.0, 1.0);
          // 使用ValueNotifier避免setState，减少整个页面重建
          if ((_scrollProgressNotifier.value - newProgress).abs() > 0.01) {
            _scrollProgressNotifier.value = newProgress;
          }
        } else {
          // 当没有滚动内容时，重置进度为0
          if (_scrollProgressNotifier.value != 0.0) {
            _scrollProgressNotifier.value = 0.0;
          }
        }
      }
    });
  }

  Future<void> _loadCategories() async {
    try {
      final forumService = ForumService();
      final response = await forumService.getCategories(
        baseUrl: HttpBaseConfig.forumBaseUrl, // 替换为实际的API域名
        context: context,
      );

      if (response.code == 0 && response.data != null) {
        setState(() {
          _categories = response.data!;
          _isLoading = false;
          _initTabController();
        });
      } else {
        setState(() {
          _error = response.message ?? '获取分类失败';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
      });
    }
  }

  void _initTabController() {
    if (_categories.isEmpty) {
      // 无分类时不初始化，避免空指针
      return;
    }
    _tabController = TabController(
      length: _categories.length,
      vsync: this,
      initialIndex: _selectedTabIndex.clamp(0, _categories.length - 1),
    );
    _tabController!.addListener(() {
      if (!mounted) return;
      setState(() {
        _selectedTabIndex = _tabController!.index;
      });
    });

    // 确保第一个tab在TabController初始化后立即刷新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _categories.isNotEmpty && _tabController != null) {
        // 通过setState触发TabBarView重建，确保第一个tab正确初始化
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _functionScrollController.dispose();
    _scrollProgressNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        // Android 状态栏图标为浅色
        statusBarBrightness: Brightness.dark,
        // iOS 状态栏内容为浅色
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        extendBodyBehindAppBar: true,
        body: Stack(
          children: [
            // 背景图 - 使用圈子配置
            Consumer<GameCircleConfigProvider>(
              builder: (context, configProvider, child) {
                final backgroundImage = configProvider.backgroundImage;

                return Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 450, // 加长背景高度，让背景延伸到 ActivityModules 下方
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image:
                            backgroundImage != null
                                ? NetworkImage(backgroundImage) as ImageProvider
                                : const AssetImage('assets/images/main_bg.png'),
                        fit: BoxFit.cover,
                        alignment: Alignment.topCenter,
                        onError:
                            backgroundImage != null
                                ? (error, stackTrace) {
                                  // 网络图片加载失败时的处理
                                  print('背景图加载失败: $error');
                                }
                                : null,
                      ),
                    ),
                  ),
                );
              },
            ),
            SafeArea(
              child: Column(
                children: [
                  // 顶部标题栏
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      children: [
                        // 游戏图标和名称 - 使用圈子配置
                        Consumer<GameCircleConfigProvider>(
                          builder: (context, configProvider, child) {
                            final gameIcon = configProvider.gameIcon;
                            final gameName = configProvider.gameName;

                            return Row(
                              children: [
                                // 游戏图标
                                gameIcon != null
                                    ? Image.network(
                                      gameIcon,
                                      width: 32,
                                      height: 32,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Image.asset(
                                          'assets/images/game_icon.png',
                                          width: 32,
                                          height: 32,
                                        );
                                      },
                                    )
                                    : Image.asset(
                                      'assets/images/game_icon.png',
                                      width: 32,
                                      height: 32,
                                    ),
                                const SizedBox(width: 8),
                                Text(
                                  gameName ?? '斗罗大陆: 猎魂世界',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                // 切换游戏圈子图标
                                GestureDetector(
                                  onTap: _handleSwitchIconClick,
                                  child: Image.asset(
                                    'assets/images/switch_icon.png',
                                    width: 20,
                                    height: 20,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                        const Spacer(),
                        // 搜索按钮
                        IconButton(
                          icon: const Icon(
                            Icons.search,
                            size: 24,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const PostSearchPage(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  // 可滚动的内容区域
                  Expanded(
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                        child:
                            _isLoading
                                ? const Center(
                                  child: CircularProgressIndicator(),
                                )
                                : NestedScrollView(
                                  // 添加 physics 配置以改善滚动行为
                                  physics: const ClampingScrollPhysics(),
                                  headerSliverBuilder: (
                                    context,
                                    innerBoxIsScrolled,
                                  ) {
                                    return [
                                      // 可折叠的用户区域 - 根据圈子类型控制显示
                                      Consumer<GameCircleConfigProvider>(
                                        builder: (
                                          context,
                                          configProvider,
                                          child,
                                        ) {
                                          if (configProvider
                                              .shouldShowUserSection) {
                                            return SliverToBoxAdapter(
                                              child: _buildUserSection(),
                                            );
                                          } else {
                                            return const SliverToBoxAdapter(
                                              child: SizedBox.shrink(),
                                            );
                                          }
                                        },
                                      ),
                                      // 可折叠的功能按钮 - 根据圈子类型控制显示
                                      Consumer<GameCircleConfigProvider>(
                                        builder: (
                                          context,
                                          configProvider,
                                          child,
                                        ) {
                                          if (configProvider
                                              .shouldShowFunctionButtons) {
                                            return SliverToBoxAdapter(
                                              child: _buildFunctionButtons(),
                                            );
                                          } else {
                                            return const SliverToBoxAdapter(
                                              child: SizedBox.shrink(),
                                            );
                                          }
                                        },
                                      ),
                                      // 可折叠的活动模块 - 使用 _activityModules 状态确保刷新
                                      SliverToBoxAdapter(
                                        child: Builder(
                                          builder: (context) {
                                            // 通过 Builder 确保在 setState 时重新构建
                                            return _buildActivityBanner();
                                          },
                                        ),
                                      ),
                                      // 吸顶的Tab栏，使用 SliverOverlapAbsorber 协调滚动
                                      SliverOverlapAbsorber(
                                        handle:
                                            NestedScrollView.sliverOverlapAbsorberHandleFor(
                                              context,
                                            ),
                                        sliver: SliverPersistentHeader(
                                          pinned: true,
                                          delegate: _SliverAppBarDelegate(
                                            _buildContentTabsOnly(),
                                            preferredHeight: 64, // 只有Tab栏的高度
                                          ),
                                        ),
                                      ),
                                    ];
                                  },
                                  body: _buildContentArea(),
                                ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserSection() {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, child) {
        // 如果正在加载角色，显示加载状态
        if (roleProvider.isLoading) {
          return Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 6),
            padding: const EdgeInsets.fromLTRB(8, 6, 8, 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.06),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.25),
                width: 1,
              ),
            ),
            child: const Row(
              children: [
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '加载中...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        // 如果有绑定的角色，显示角色信息
        if (roleProvider.hasBoundRoles && roleProvider.hasSelectedRole) {
          return _buildBoundCharacterSection(roleProvider.selectedRole!);
        }

        // 如果没有绑定角色，显示未绑定状态
        return _buildUnboundCharacterSection();
      },
    );
  }


  /// 构建未绑定角色的UI
  Widget _buildUnboundCharacterSection() {
    return GestureDetector(
      onTap: _navigateToSwitchBindCharacter,
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 6),
        padding: const EdgeInsets.fromLTRB(8, 6, 8, 6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.06),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.25),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            GestureDetector(
              onTap: _navigateToSwitchBindCharacter,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.asset(
                    'assets/images/unbind_icon.png',
                    width: 40,
                    height: 40,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '未绑定角色',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: _navigateToSwitchBindCharacter,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: DLColor.tagBackground,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Text(
                  '绑定角色领礼包',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建已绑定角色的UI
  Widget _buildBoundCharacterSection(GameRoleV2 selectedRole) {

    return GestureDetector(
      onTap: () => SwitchBindRoleDialog.showCharacterSwitchDialog(
        context,
        onCharacterSwitched: _refreshPageData,
        onNavigateToBindCharacter: _navigateToSwitchBindCharacter,
      ),
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 6),
        padding: const EdgeInsets.fromLTRB(8, 6, 8, 6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.06),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.25),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 角色头像容器 - 使用Stack来叠加切换图标
            Container(
              width: 44, // 增加宽度为切换图标留出空间
              height: 44, // 增加高度为切换图标留出空间
              child: Stack(
                clipBehavior: Clip.none, // 允许子组件超出边界
                children: [
                  // 角色头像
                  Positioned(
                    left: 0,
                    top: 0,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Image.asset(
                          'assets/images/avatar.png',
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              'assets/images/unbind_icon.png',
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  // 切换图标 - 位于头像右下角
                  Positioned(
                    bottom: 0,
                    right: 2,
                    child: GestureDetector(
                      onTap: () => SwitchBindRoleDialog.showCharacterSwitchDialog(
                        context,
                        onCharacterSwitched: _refreshPageData,
                        onNavigateToBindCharacter: _navigateToSwitchBindCharacter,
                      ),
                      child: Container(
                        width: 18,
                        height: 18,
                        decoration: BoxDecoration(
                          // color: Colors.white,
                          borderRadius: BorderRadius.circular(9),
                          boxShadow: [
                            // BoxShadow(
                            //   color: Colors.black.withValues(alpha: 0.2),
                            //   blurRadius: 2,
                            //   offset: const Offset(0, 1),
                            // ),
                          ],
                        ),
                        child: Center(
                          child: Image.asset(
                            'assets/images/transfer.png',
                            width: 16,
                            height: 16,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.swap_horiz,
                                color: Colors.grey,
                                size: 12,
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8), // 减少间距因为容器变宽了
            // 角色信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    selectedRole.drname,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    selectedRole.dsname,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: () => SwitchBindRoleDialog.showCharacterSwitchDialog(
                context,
                onCharacterSwitched: _refreshPageData,
                onNavigateToBindCharacter: _navigateToSwitchBindCharacter,
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: DLColor.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                side: BorderSide.none,
                minimumSize: const Size(85, 36),
                maximumSize: const Size(85, 36),
              ),
              child: const Text(
                '打开游戏',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionButtons() {
    LogUtil.d(
      '_buildFunctionButtons 被调用，_functionAreas.length=${_functionAreas.length}',
      tag: 'CommunityPage',
    );
    LogUtil.d('_functionAreas 内容: $_functionAreas', tag: 'CommunityPage');

    // 如果没有功能区数据，不显示任何内容
    if (_functionAreas.isEmpty) {
      LogUtil.d('功能区数据为空，不显示功能按钮区域', tag: 'CommunityPage');
      return const SizedBox.shrink();
    }

    LogUtil.d('显示接口数据功能按钮', tag: 'CommunityPage');

    // 计算需要的行数
    const itemsPerRow = 4;
    const rowHeight = 86.0; // 增加行高以适应60x60图标 + 文字 (60 + 8 + 18 = 86)
    const rowSpacing = 8.0; // 行间距
    final rowCount = (_functionAreas.length / itemsPerRow).ceil();
    final maxDisplayRows = 2; // 最多显示2行，超过的可以滚动
    final actualDisplayRows =
        rowCount > maxDisplayRows ? maxDisplayRows : rowCount;
    final containerHeight =
        (actualDisplayRows * rowHeight) +
        ((actualDisplayRows - 1) * rowSpacing);

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16), // 减少底部边距，从28改为16
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.06),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.white.withOpacity(0.25), width: 1),
      ),
      child: SizedBox(
        height: containerHeight,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: _buildFunctionGrid(),
        ),
      ),
    );
  }

  Widget _buildFunctionGrid() {
    const itemsPerRow = 4;
    final rows = <Widget>[];

    for (int i = 0; i < _functionAreas.length; i += itemsPerRow) {
      final rowItems = _functionAreas.skip(i).take(itemsPerRow).toList();
      rows.add(_buildFunctionRow(rowItems));

      // 如果不是最后一行，添加行间距
      if (i + itemsPerRow < _functionAreas.length) {
        rows.add(const SizedBox(height: 8));
      }
    }

    return Column(children: rows);
  }

  Widget _buildFunctionRow(List<FunctionArea> rowItems) {
    return SizedBox(
      height: 86, // 增加固定行高以匹配新的按钮尺寸 (60px图标 + 8px间距 + 18px文字)
      child: Row(
        children: [
          ...rowItems.asMap().entries.map((entry) {
            int index = entry.key;
            FunctionArea area = entry.value;
            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: index < rowItems.length - 1 ? 8 : 0,
                ),
                child: Center(
                  // 添加Center让item垂直居中
                  child: _buildFunctionButtonFromArea(area),
                ),
              ),
            );
          }),
          // 如果这一行不足4个，用空的Expanded填充
          ...List.generate(
            4 - rowItems.length,
            (index) => const Expanded(child: SizedBox()),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionButton(
    String title,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(Icons.card_giftcard, color: color, size: 24),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          if (subtitle.isNotEmpty)
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFunctionButtonFromArea(FunctionArea area) {
    return GestureDetector(
      onTap: () => _handleFunctionAreaClick(area),
      child: Stack(
        clipBehavior: Clip.none, // 允许角标超出边界
        children: [
          // 主要内容：图标和文字
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 主图标容器 - 增大到60x60以匹配设计
              Container(
                width: 60,
                height: 60,
                child:
                    area.iconUrl.isNotEmpty
                        ? Image.network(
                          area.iconUrl,
                          width: 40,
                          height: 40,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.card_giftcard,
                              color: Colors.white,
                              size: 30,
                            );
                          },
                        )
                        : const Icon(
                          Icons.card_giftcard,
                          color: Colors.white,
                          size: 30,
                        ),
              ),
              const SizedBox(height: 4),
              // 文字容器
              SizedBox(
                height: 18, // 稍微增加文字区域高度
                child: Text(
                  area.areaName,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          // 角标图标 - 根据截图调整位置到左上角
          if (area.badgeUrl.isNotEmpty)
            Positioned(
              top: -20,
              left: 30, // 调整到左上角位置
              child: Container(
                width: 45,
                height: 40, // 椭圆形角标
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Image.network(
                  area.badgeUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActivityModulesAndTabs() {
    const BorderRadius topRadius = BorderRadius.only(
      topLeft: Radius.circular(12),
      topRight: Radius.circular(12),
    );

    return ClipRRect(
      borderRadius: topRadius,
      child: Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min, // 让Column自适应内容高度
          children: [
            // ActivityModules 部分
            _buildActivityBanner(),
            // Tabs 部分
            _buildContentTabs(),
          ],
        ),
      ),
    );
  }

  /// 只包含Tab栏的吸顶组件
  Widget _buildContentTabsOnly() {
    if (_error != null || _categories.isEmpty || _tabController == null)
      return const SizedBox.shrink();

    return Container(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
        height: 48, // 明确设置TabBar高度
        child: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          tabs:
              _categories.map((category) => Tab(text: category.name)).toList(),
          labelColor: Colors.black87,
          unselectedLabelColor: Colors.grey[600],
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 16,
          ),
          // 使用自定义图片指示器，自动跟随选中tab并居中显示
          indicator: ImageTabIndicator(
            image: _tabIndicatorAsset,
            imageSize: const Size(40, 12),
            bottomPadding: 0,
          ),
          dividerColor: Colors.grey[300],
          dividerHeight: 1,
          splashFactory: NoSplash.splashFactory,
          overlayColor: MaterialStateProperty.all(Colors.transparent),
          labelPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        ),
      ),
    );
  }

  Widget _buildContentTabs() {
    if (_categories.isEmpty || _tabController == null)
      return const SizedBox.shrink();

    return Container(
      color: Colors.white,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        // 进一步减少垂直margin
        height: 48,
        // 明确设置TabBar高度
        child: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          tabs:
              _categories.map((category) => Tab(text: category.name)).toList(),
          labelColor: Colors.black87,
          unselectedLabelColor: Colors.grey[600],
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 16,
          ),
          // 使用自定义图片指示器，自动跟随选中tab并居中显示
          indicator: ImageTabIndicator(
            image: _tabIndicatorAsset,
            imageSize: const Size(24, 8),
            bottomPadding: 4,
          ),
          dividerColor: Colors.grey[300],
          dividerHeight: 1,
          splashFactory: NoSplash.splashFactory,
          overlayColor: MaterialStateProperty.all(Colors.transparent),
          labelPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 4,
          ), // 减少垂直padding
        ),
      ),
    );
  }

  Widget _buildContentArea() {
    if (_error != null) {
      // 当有错误时，显示错误信息而不是Tab内容
      return Container(
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                _error ?? '加载失败',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isLoading = true;
                    _error = null;
                  });
                  _loadCategories();
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    if (_categories.isEmpty || _tabController == null)
      return const SizedBox.shrink();

    // 使用不透明的背景色包裹，避免下拉刷新时透明区域透出底层 main_bg.png
    return Container(
      color: Colors.white,
      child: TabBarView(
        controller: _tabController,
        // 使用 ClampingScrollPhysics 以改善与 NestedScrollView 的协作
        physics: const ClampingScrollPhysics(),
        children:
            _categories.asMap().entries.map((entry) {
              int index = entry.key;
              ForumCategory category = entry.value;

              // 根据分类名称返回不同的页面内容
              return _getContentForCategory(category, index);
            }).toList(),
      ),
    );
  }

  Widget _getContentForCategory(ForumCategory category, int index) {
    // 检查缓存中是否已经有这个页面
    if (_cachedTabPages.containsKey(index)) {
      return _cachedTabPages[index]!;
    }

    // 使用 FutureBuilder 获取并构建头部组件后，传入帖子页面
    final forumService = ForumService();
    final future = forumService.getActLabel(
      baseUrl: HttpBaseConfig.forumBaseUrl,
      categoryId: category.categoryId,
      context: context,
    );

    final pageWidget = FutureBuilder(
      future: future,
      builder: (context, snapshot) {
        List<Widget> headerWidgets = [];
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.hasData) {
          final resp = snapshot.data!;
          if (resp.code == 0 && resp.data != null) {
            headerWidgets = _buildHeaderWidgetsFromActLabels(resp.data!);
          }
        }

        // 包装子页面以确保与 NestedScrollView 正确协作
        Widget childPage;
        // 根据分类的threadStyle决定显示哪种页面类型
        // threadStyle为"waterfall"时显示瀑布流页面，否则显示普通列表页面
        if (category.threadStyle == '2') {
          childPage = ForumPostWaterfallListPage(
            key: ValueKey('waterfall_${category.categoryId}'),
            category: category,
            headerWidgets: headerWidgets,
            onRefreshHeader: () => _refreshHeaderForCategory(index),
          );
        } else {
          childPage = ForumPostListPage(
            key: ValueKey('forum_${category.categoryId}'),
            category: category,
            headerWidgets: headerWidgets,
            onRefreshHeader: () => _refreshHeaderForCategory(index),
          );
        }

        // 使用 Builder 包装以确保正确的滚动行为
        return Builder(
          builder: (context) {
            return childPage;
          },
        );
      },
    );

    _cachedTabPages[index] = pageWidget;
    return pageWidget;
  }

  List<Widget> _buildHeaderWidgetsFromActLabels(
    List<Map<String, dynamic>> labels,
  ) {
    // 按 sort 升序
    labels.sort((a, b) => (a['sort'] ?? 0).compareTo(b['sort'] ?? 0));
    final widgets = <Widget>[];
    for (final item in labels) {
      final style = (item['style_name'] ?? '').toString();
      final title = (item['title'] ?? '').toString();
      final count =
          (item['count'] ?? 0) is int
              ? item['count'] as int
              : int.tryParse(item['count'].toString()) ?? 0;
      final contentStr = (item['content'] ?? '[]').toString();
      List<dynamic> contentList = [];
      
      // 添加调试信息
      print('=== 解析 ActLabel 数据 ===');
      print('style: $style');
      print('title: $title');
      print('contentStr: $contentStr');
      
      try {
        contentList = json.decode(contentStr) as List<dynamic>;
        print('解析成功，数据数量: ${contentList.length}');
        for (int i = 0; i < contentList.length; i++) {
          print('第$i个元素: ${contentList[i]}');
        }
      } catch (e) {
        print('解析 JSON 失败: $e');
      }
      print('==============================');

      if (style == '推荐帖子列表') {
        widgets.add(_buildRecommendList(title, contentList));
      } else if (style == '活动卡片') {
        widgets.add(_buildActivityCards(title, contentList, count));
      } else if (style == '话题卡片') {
        widgets.add(_buildTopicCards(title, contentList, count));
      } else if (style == '壁纸卡片') {
        // 获取 wallpaper_style 字段，判断横屏还是竖屏
        final wallpaperStyle = (item['wallpaper_style'] ?? 1) is int
            ? item['wallpaper_style'] as int
            : int.tryParse(item['wallpaper_style'].toString()) ?? 1;
        widgets.add(_buildWallpaperCards(title, contentList, wallpaperStyle));
      }
    }
    return widgets;
  }

  Widget _sectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendList(String title, List<dynamic> items) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.only(bottom: 12),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 只有标题不为空时才显示标题
          if (title.trim().isNotEmpty) _sectionTitle(title),
          Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.only(
              left: 0,
              right: 12,
              top: 12,
              bottom: 2,
            ),
            color: Colors.white,
            child: Column(
              children:
                  items.map((e) {
                    final map = Map<String, dynamic>.from(e as Map);
                    final header = (map['header'] ?? '').toString();
                    final display = (map['display'] ?? '').toString();
                    final link = (map["link"] ?? '').toString();
                    final icon = (map["icon"] ?? '').toString();

                    return Container(
                      margin: const EdgeInsets.only(bottom: 10),
                      child: GestureDetector(
                        onTap: () => _handleActivityClick(link, display),
                        child: Stack(
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.orange,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    header,
                                    style: const TextStyle(
                                      color: Colors.orange,
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    display,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      fontSize: 13,
                                      color: Colors.black87,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // 右上角图标
                            if (icon.isNotEmpty)
                              Positioned(
                                top: 0,
                                right: 0,
                                child: Container(
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 2,
                                        offset: const Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: Image.network(
                                      icon,
                                      width: 20,
                                      height: 20,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Container(
                                          width: 20,
                                          height: 20,
                                          decoration: BoxDecoration(
                                            color: Colors.grey[300],
                                            borderRadius: BorderRadius.circular(
                                              10,
                                            ),
                                          ),
                                          child: const Icon(
                                            Icons.image_not_supported,
                                            size: 12,
                                            color: Colors.grey,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityCards(String title, List<dynamic> items, int count) {
    final perRow = count.clamp(1, 5);
    final rows = <List<Map<String, dynamic>>>[];
    final parsed =
        items.map((e) => Map<String, dynamic>.from(e as Map)).toList();
    for (int i = 0; i < parsed.length; i += perRow) {
      rows.add(
        parsed.sublist(
          i,
          (i + perRow > parsed.length) ? parsed.length : i + perRow,
        ),
      );
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 只有标题不为空时才显示标题
          if (title.trim().isNotEmpty) _sectionTitle(title),
          Column(
            children:
                rows.map((row) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        ...row.map(
                          (m) => Expanded(
                            child: GestureDetector(
                              onTap:
                                  () => _handleActivityClick(
                                    (m['link'] ?? '').toString(),
                                    (m['desc'] ?? '').toString(),
                                  ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  // 纯图片卡片
                                  Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    child: Stack(
                                      clipBehavior: Clip.none, // 允许子组件超出边界
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withOpacity(
                                                  0.04,
                                                ),
                                                blurRadius: 6,
                                                offset: const Offset(0, 1),
                                              ),
                                            ],
                                          ),
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            child: Image.network(
                                              (m['cover'] ?? '').toString(),
                                              fit: BoxFit.fitWidth,
                                              width: double.infinity,
                                              errorBuilder:
                                                  (c, e, s) => Container(
                                                    height: 120,
                                                    color: Colors.grey[200],
                                                    child: const Icon(
                                                      Icons.image_not_supported,
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                            ),
                                          ),
                                        ),
                                        // 右上角图标
                                        if ((m['icon'] ?? '')
                                            .toString()
                                            .isNotEmpty)
                                          Positioned(
                                            top: -4,
                                            right: -4,
                                            child: Container(
                                              width: 20,
                                              height: 20,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.2),
                                                    blurRadius: 3,
                                                    offset: const Offset(0, 1),
                                                  ),
                                                ],
                                              ),
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                child: Image.network(
                                                  (m['icon'] ?? '').toString(),
                                                  width: 20,
                                                  height: 20,
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (
                                                    context,
                                                    error,
                                                    stackTrace,
                                                  ) {
                                                    return Container(
                                                      width: 20,
                                                      height: 20,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[300],
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              10,
                                                            ),
                                                      ),
                                                      child: const Icon(
                                                        Icons
                                                            .image_not_supported,
                                                        size: 12,
                                                        color: Colors.grey,
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                  // 文本独立在图片下方（只有有内容时才显示）
                                  if ((m['desc'] ?? '')
                                      .toString()
                                      .trim()
                                      .isNotEmpty) ...[
                                    const SizedBox(height: 6),
                                    Text(
                                      (m['desc'] ?? '').toString(),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.left,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                        // 填充空位
                        ...List.generate(
                          perRow - row.length,
                          (i) => const Expanded(child: SizedBox.shrink()),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicCards(String title, List<dynamic> items, int count) {
    final perRow = count.clamp(1, 5);
    final parsed =
        items.map((e) => Map<String, dynamic>.from(e as Map)).toList();
    final rows = <List<Map<String, dynamic>>>[];
    for (int i = 0; i < parsed.length; i += perRow) {
      rows.add(
        parsed.sublist(
          i,
          (i + perRow > parsed.length) ? parsed.length : i + perRow,
        ),
      );
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 只有标题不为空时才显示标题
          if (title.trim().isNotEmpty) _sectionTitle(title),
          Column(
            children:
                rows.map((row) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 0),
                    child: Row(
                      children: [
                        ...row.map(
                          (m) => Expanded(
                            child: GestureDetector(
                              onTap: () {
                                final Map<String, dynamic> data = m;
                                String topicName =
                                    (data['topicName'] ??
                                            data['topic_name'] ??
                                            '')
                                        .toString();
                                int? topicId = int.tryParse(
                                  (data['topicId'] ?? data['topic_id'] ?? '')
                                      .toString(),
                                );
                                String? desc;
                                final dynamic d =
                                    data['topicDesc'] ??
                                    data['desc'] ??
                                    data['description'];
                                if (d != null) desc = d.toString();
                                String? avatar;
                                final dynamic a =
                                    data['cover'] ??
                                    data['avatar'] ??
                                    data['icon'];
                                if (a != null && a.toString().isNotEmpty)
                                  avatar = a.toString();

                                int parseInt(dynamic v) {
                                  if (v is int) return v;
                                  if (v == null) return 0;
                                  return int.tryParse(v.toString()) ?? 0;
                                }

                                int threadCount = parseInt(
                                  data['threadCount'] ?? data['thread_count'],
                                );
                                int viewCount = parseInt(
                                  data['view_count'] ?? data['viewCount'],
                                );
                                int likeCount = parseInt(
                                  data['like_count'] ?? data['likeCount'],
                                );
                                if (data['topicData'] is Map) {
                                  final td = Map<String, dynamic>.from(
                                    data['topicData'] as Map,
                                  );
                                  threadCount =
                                      threadCount == 0
                                          ? parseInt(
                                            td['threadCount'] ??
                                                td['thread_count'],
                                          )
                                          : threadCount;
                                  viewCount =
                                      viewCount == 0
                                          ? parseInt(
                                            td['view_count'] ?? td['viewCount'],
                                          )
                                          : viewCount;
                                  likeCount =
                                      likeCount == 0
                                          ? parseInt(
                                            td['like_count'] ?? td['likeCount'],
                                          )
                                          : likeCount;
                                }

                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => TopicPage(
                                          topicName:
                                              topicName.isEmpty
                                                  ? '话题'
                                                  : topicName,
                                          topicId: topicId,
                                          description: desc,
                                          avatarUrl: avatar,
                                          viewCount: viewCount,
                                          likeCount: likeCount,
                                          threadCount: threadCount,
                                        ),
                                  ),
                                );
                              },
                              child: Container(
                                margin: const EdgeInsets.only(right: 4),
                                height: 60,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    left: 0,
                                    top: 0,
                                    right: 6,
                                    bottom: 0,
                                  ),
                                  child: Row(
                                    children: [
                                      if ((m['cover'] ?? '')
                                          .toString()
                                          .isNotEmpty)
                                        Container(
                                          width: 44, // 增加容器宽度，为icon角标留出空间
                                          height: 44, // 增加容器高度，为icon角标留出空间
                                          child: Stack(
                                            clipBehavior:
                                                Clip.none, // 允许子组件超出边界
                                            children: [
                                              Positioned(
                                                left: 0,
                                                top: 0,
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  child: Image.network(
                                                    (m['cover'] ?? '')
                                                        .toString(),
                                                    width: 40,
                                                    height: 40,
                                                    fit: BoxFit.cover,
                                                    errorBuilder:
                                                        (c, e, s) => Container(
                                                          width: 40,
                                                          height: 40,
                                                          color:
                                                              Colors.grey[200],
                                                        ),
                                                  ),
                                                ),
                                              ),
                                              // cover图标的右上角图标
                                              if ((m['icon'] ?? '')
                                                  .toString()
                                                  .isNotEmpty)
                                                Positioned(
                                                  top: -2,
                                                  right: -2,
                                                  child: Container(
                                                    width: 12,
                                                    height: 12,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            8,
                                                          ),
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.black
                                                              .withOpacity(0.2),
                                                          blurRadius: 2,
                                                          offset: const Offset(
                                                            0,
                                                            1,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    child: ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            8,
                                                          ),
                                                      child: Image.network(
                                                        (m['icon'] ?? '')
                                                            .toString(),
                                                        width: 16,
                                                        height: 16,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return Container(
                                                            width: 16,
                                                            height: 16,
                                                            decoration: BoxDecoration(
                                                              color:
                                                                  Colors
                                                                      .grey[300],
                                                              borderRadius:
                                                                  BorderRadius.circular(
                                                                    8,
                                                                  ),
                                                            ),
                                                            child: const Icon(
                                                              Icons
                                                                  .image_not_supported,
                                                              size: 8,
                                                              color:
                                                                  Colors.grey,
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      const SizedBox(width: 0),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              '#${(m['topicName'] ?? '').toString()}',
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: const TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                top: 2,
                                              ),
                                              child: Text(
                                                _topicStatsText(m),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyle(
                                                  fontSize: 9,
                                                  color: Colors.grey[600],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        ...List.generate(
                          perRow - row.length,
                          (i) => const Expanded(child: SizedBox.shrink()),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建壁纸卡片组件
  /// wallpaperStyle: 1=横屏(一行2个), 2=竖屏(一行3个)
  Widget _buildWallpaperCards(String title, List<dynamic> items, int wallpaperStyle) {
    // 根据壁纸样式确定每行显示的数量
    final int perRow = wallpaperStyle == 1 ? 2 : 3; // 横屏一行2个，竖屏一行3个
    // 设置统一的最小高度，确保同一行图片高度一致
    final double minHeight = wallpaperStyle == 1 ? 120.0 : 160.0; // 横屏较矮，竖屏较高
    
    final parsed = items.map((e) => Map<String, dynamic>.from(e as Map)).toList();
    final rows = <List<Map<String, dynamic>>>[];
    
    // 分组处理
    for (int i = 0; i < parsed.length; i += perRow) {
      rows.add(
        parsed.sublist(
          i,
          (i + perRow > parsed.length) ? parsed.length : i + perRow,
        ),
      );
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区域
          if (title.trim().isNotEmpty) 
            Container(
              padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
              child: Row(
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          // 壁纸网格
          Container(
            child: Column(
              children: rows.map((row) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  height: minHeight, // 设置固定行高
                  child: Row(
                    children: [
                      ...row.asMap().entries.map((entry) {
                        final int index = entry.key;
                        final Map<String, dynamic> wallpaper = entry.value;
                        
                        return Expanded(
                          child: Container(
                            margin: EdgeInsets.only(
                              right: index < row.length - 1 ? 8 : 0,
                            ),
                            height: minHeight, // 确保高度一致
                            child: _buildWallpaperItem(wallpaper, minHeight),
                          ),
                        );
                      }),
                      // 填充空位
                      ...List.generate(
                        perRow - row.length,
                        (i) => const Expanded(child: SizedBox()),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个壁纸项
  Widget _buildWallpaperItem(Map<String, dynamic> wallpaper, double fixedHeight) {
    // 添加调试信息
    print('=== 壁纸数据调试 ===');
    print('壁纸数据: $wallpaper');
    
    final String thumbUrl = (wallpaper['thumbLink'] ?? wallpaper['cover'] ?? wallpaper['image'] ?? '').toString();
    final String fullUrl = (wallpaper['link'] ?? wallpaper['cover'] ?? wallpaper['image'] ?? '').toString();
    final String title = (wallpaper['title'] ?? wallpaper['desc'] ?? '').toString();
    
    print('缩略图 URL: $thumbUrl');
    print('全尺寸 URL: $fullUrl');
    print('标题: $title');
    print('========================');
    
    return GestureDetector(
      onTap: () {
        print('点击壁纸，将显示 URL: $fullUrl');
        _handleWallpaperClick('', title, fullUrl);
      },
      child: Container(
        height: fixedHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 壁纸缩略图使用CachedImage，不传递onTap避免事件冲突
              thumbUrl.isNotEmpty
                  ? CachedImage(
                      imageUrl: thumbUrl,
                      width: double.infinity,
                      height: fixedHeight,
                      fit: BoxFit.cover, // 使用cover确保填满容器
                      // 不传递onTap，让点击事件由外层GestureDetector处理
                    )
                  : Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.wallpaper,
                        color: Colors.grey[400],
                        size: 40,
                      ),
                    ),
              // 底部渐变遮罩和标题
              if (title.isNotEmpty)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理壁纸点击事件
  void _handleWallpaperClick(String link, String title, String imageUrl) {
    print('处理壁纸点击: imageUrl=$imageUrl, title=$title');
    
    if (imageUrl.isEmpty) {
      print('壁纸 URL 为空，无法显示全屏预览');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('图片加载失败'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    // 壁纸卡片点击总是显示全屏预览
    _showWallpaperPreview(imageUrl, title);
  }

  /// 显示壁纸预览
  void _showWallpaperPreview(String imageUrl, String title) {
    if (imageUrl.isEmpty) return;
    
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog.fullscreen(
          backgroundColor: Colors.black,
          child: Stack(
            children: [
              // 图片预览 - 使用CachedImage显示全分辨率图片
              Center(
                child: InteractiveViewer(
                  panEnabled: true,
                  scaleEnabled: true,
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: CachedImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.contain,
                    onTap: () {}, // 空回调，避免干扰预览功能
                  ),
                ),
              ),
              // 关闭按钮
              Positioned(
                top: 50,
                right: 20,
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
              // 底部按钮区域
              Positioned(
                bottom: 50,
                left: 20,
                right: 20,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // 标题信息（如果有）
                    if (title.isNotEmpty)
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(right: 12),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    // 保存按钮
                    GestureDetector(
                      onTap: () => _saveWallpaper(imageUrl, title),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.download,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 4),
                            const Text(
                              '保存',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 保存壁纸到本地
  Future<void> _saveWallpaper(String imageUrl, String title) async {
    try {
      // 显示保存中提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在保存壁纸...'),
          duration: Duration(seconds: 2),
        ),
      );
      
      // TODO: 实现图片保存功能
      // 这里需要添加实际的图片下载和保存逻辑
      // 可以使用 image_gallery_saver 或 permission_handler + http 等包
      
      // 模拟保存延迟
      await Future.delayed(const Duration(seconds: 1));
      
      // 显示保存成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('壁纸"${title.isNotEmpty ? title : '图片'}"保存成功'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 显示保存失败提示
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  String _topicStatsText(Map<String, dynamic> item) {
    int? readInt(Map<String, dynamic> map, List<String> keys) {
      for (final key in keys) {
        final dynamic value = map[key];
        if (value is int) return value;
        if (value != null) {
          final parsed = int.tryParse(value.toString());
          if (parsed != null) return parsed;
        }
      }
      return null;
    }

    String? readStr(Map<String, dynamic> map, List<String> keys) {
      for (final key in keys) {
        final dynamic value = map[key];
        if (value != null) {
          final s = value.toString();
          if (s.isNotEmpty) return s;
        }
      }
      return null;
    }

    // 顶层优先
    int? threadCount = readInt(item, ['threadCount', 'thread_count']);
    String? viewCount = readStr(item, ['view_count', 'viewCount']);
    String? likeCount = readStr(item, ['like_count', 'likeCount']);

    // 回退 topicData（不再依赖 showTopicData 的取值，只要有数据就用）
    if (item['topicData'] is Map) {
      final data = Map<String, dynamic>.from(item['topicData'] as Map);
      threadCount ??= readInt(data, ['threadCount', 'thread_count']);
      viewCount ??= readStr(data, ['view_count', 'viewCount']);
      likeCount ??= readStr(data, ['like_count', 'likeCount']);
    }

    // 兜底
    threadCount ??= 0;
    viewCount ??= '0';
    likeCount ??= '0';

    return '${viewCount}浏览 · ${likeCount}点赞 · ${threadCount}帖';
  }

  // 添加一个方法来清除缓存（供下拉刷新使用）
  void _clearTabCache() {
    _cachedTabPages.clear();
    _cachedActivityIcons.clear(); // 同时清理活动图标缓存
  }

  // 添加一个方法来清除特定tab的缓存
  void _clearTabCacheAt(int index) {
    _cachedTabPages.remove(index);
  }

  /// 刷新指定分类的头部数据
  void _refreshHeaderForCategory(int index) {
    // 清除该分类的缓存，强制重新构建
    _clearTabCacheAt(index);
    // 触发重建以刷新UI
    if (mounted) {
      setState(() {});
    }
  }

  void _navigateToCharacterBinding() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => CharacterBindPage(
              phoneNumber: '138****8888', // 替换为实际的手机号
              uid: 'test_uid', // 替换为实际的用户ID
              allRoles: const [], // 临时空列表，实际需要获取真实数据
              boundRoles: const [], // 临时空列表，实际需要获取真实数据
            ),
      ),
    );
  }

  void _navigateToSwitchBindCharacter() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SwitchBindCharacterPage()),
    ).then((_) {
      // 从绑定页面返回后，重新加载角色数据
      Provider.of<RoleProvider>(context, listen: false).loadBoundRoles(context);
    });
  }

  /// 刷新页面数据
  Future<void> _refreshPageData() async {
    // 重新加载角色数据
    await Provider.of<RoleProvider>(context, listen: false).loadBoundRoles(context);

    // 重新初始化论坛配置和数据
    await _initializeForumAndLoadData();

    // 清除tab缓存，强制刷新
    _clearTabCache();
  }

  void _handleFunctionAreaClick(FunctionArea area) {
    LogUtil.d(
      '点击功能区: ${area.areaName}, 模块: ${area.areaModule}, 打开方式: ${area.areaOpenWay}, 入口: ${area.areaEntrance}',
    );

    // 根据不同的打开方式进行处理
    switch (area.areaOpenWay.toLowerCase()) {
      case 'h5':
        // 使用WebViewDialog打开
        if (area.areaEntrance.isNotEmpty) {
          showDialog(
            context: context,
            builder:
                (context) =>
                    WebViewDialog(url: area.areaEntrance, title: area.areaName),
          );
        } else {
          _showComingSoonDialog(area.areaName);
        }
        break;
      case 'webview':
      case 'web':
        // 使用webview打开
        if (area.areaEntrance.isNotEmpty) {
          WebRouter.jumpToWebPage(
            context,
            area.areaEntrance,
            area.areaName,
            {},
          );
        } else {
          _showComingSoonDialog(area.areaName);
        }
        break;
      case 'native':
      case 'page':
        // 原生页面跳转
        _handleNativePageJump(area);
        break;
      default:
        // 默认使用webview打开
        if (area.areaEntrance.isNotEmpty) {
          WebRouter.jumpToWebPage(
            context,
            area.areaEntrance,
            area.areaName,
            {},
          );
        } else {
          _showComingSoonDialog(area.areaName);
        }
        break;
    }
  }

  void _handleNativePageJump(FunctionArea area) {
    // 根据不同的模块进行原生页面跳转
    switch (area.areaModule) {
      case 'voucher':
      case '代金券':
        _jumpToVoucherPage();
        break;
      case 'gift':
      case '礼包中心':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const GiftCenterPage()),
        );
        break;
      default:
        // 未知模块，显示敬请期待
        _showComingSoonDialog(area.areaName);
        break;
    }
  }

  Future<void> _jumpToVoucherPage() async {
    //todo 待完善params，替换url
    final voucherUrl = 'http://user.37.com.cn/sdkv1/coupon/sdk/index';
    WebRouter.jumpToWebPage(context, voucherUrl, '代金券', {});
  }

  void _showComingSoonDialog(String featureName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('功能开发中'),
          content: Text('$featureName 功能正在开发中，敬请期待！'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActivityBanner() {
    if (_activityModules.isEmpty) {
      return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
        ),
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
          height: 120,
          child: const Center(
            child: Text('暂无活动', style: TextStyle(color: Colors.grey)),
          ),
        ),
      );
    }

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 10, 16, 0),
        child: Column(
          children: [
            // 功能图标滑动区域
            SizedBox(
              height: 80,
              child: Align(
                alignment: Alignment.centerLeft,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  controller: _functionScrollController,
                  // 使用BouncingScrollPhysics提供更流畅的滚动体验
                  physics: const BouncingScrollPhysics(),
                  // 优化滚动性能
                  clipBehavior: Clip.none,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children:
                        _activityModules
                            .map((module) => _buildActivityIcon(module))
                            .toList(),
                  ),
                ),
              ),
            ),

            // 滑动指示器
            const SizedBox(height: 8),
            _buildScrollIndicator(),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildScrollIndicator() {
    // 始终显示指示器，除非没有数据
    if (_activityModules.isEmpty) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder<double>(
      valueListenable: _scrollProgressNotifier,
      builder: (context, scrollProgress, child) {
        return Container(
          width: 30,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(3),
          ),
          child: Stack(
            children: [
              // 指示器背景
              Container(
                width: 30,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
              // 指示器前景（滑动进度）- 使用Transform减少重建
              Transform.translate(
                offset: Offset((30 - 20) * scrollProgress, 0),
                child: Container(
                  width: 20,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActivityIcon(ActModule module) {
    // 使用缓存避免重复构建
    final cacheKey = '${module.icon}_${module.actName}';
    if (_cachedActivityIcons.containsKey(cacheKey)) {
      return GestureDetector(
        onTap: () => _handleActivityClick(module.url, module.actName),
        child: _cachedActivityIcons[cacheKey]!,
      );
    }

    final iconWidget = Container(
      width: 80,
      margin: const EdgeInsets.fromLTRB(4, 0, 4, 0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 图标容器 - 使用RepaintBoundary隔离重绘
          RepaintBoundary(
            child: Container(
              width: 42,
              height: 42,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Image.network(
                  module.icon,
                  width: 48,
                  height: 48,
                  fit: BoxFit.cover,
                  // 添加缓存配置
                  cacheWidth: 96,
                  // 2x density
                  cacheHeight: 96,
                  // 添加帧缓存优化
                  frameBuilder: (
                    context,
                    child,
                    frame,
                    wasSynchronouslyLoaded,
                  ) {
                    if (wasSynchronouslyLoaded || frame != null) {
                      return child;
                    }
                    return Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          // 主标题 - 使用RepaintBoundary隔离重绘
          RepaintBoundary(
            child: Text(
              module.actName,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );

    // 缓存构建的widget
    _cachedActivityIcons[cacheKey] = iconWidget;

    return GestureDetector(
      onTap: () => _handleActivityClick(module.url, module.actName),
      child: iconWidget,
    );
  }

  /// 处理活动点击事件 - 内部方法
  void _handleActivityClick(String link, String title) {
    ActivityLinkHandler.handleActivityClick(context, link, title);
  }
  
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double preferredHeight;

  _SliverAppBarDelegate(this.child, {required this.preferredHeight});

  @override
  double get minExtent => preferredHeight;

  @override
  double get maxExtent => preferredHeight;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    // 直接返回Tab栏，不需要额外的偏移和圆角处理
    return child;
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    // 需要随着外部状态（如滚动进度指示器）变化而重建
    return true;
  }
}

/// 使用图片的Tab指示器，图片会绘制在选中Tab的下方并水平居中
class ImageTabIndicator extends Decoration {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  const ImageTabIndicator({
    required this.image,
    required this.imageSize,
    this.bottomPadding = 4,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ImageTabIndicatorPainter(
      image: image,
      imageSize: imageSize,
      bottomPadding: bottomPadding,
      onChanged: onChanged,
    );
  }
}

class _ImageTabIndicatorPainter extends BoxPainter {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  ImageStream? _imageStream;
  ImageInfo? _imageInfo;
  ImageStreamListener? _imageStreamListener;

  _ImageTabIndicatorPainter({
    required this.image,
    required this.imageSize,
    required this.bottomPadding,
    VoidCallback? onChanged,
  }) : super(onChanged);

  void _resolveImage(ImageConfiguration configuration) {
    final ImageStream newStream = image.resolve(configuration);
    if (_imageStream?.key == newStream.key) {
      return;
    }
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = newStream;
    _imageStreamListener ??= ImageStreamListener(
      _handleImage,
      onError: _handleError,
    );
    _imageStream!.addListener(_imageStreamListener!);
  }

  void _handleImage(ImageInfo imageInfo, bool synchronousCall) {
    _imageInfo = imageInfo;
    onChanged?.call();
  }

  void _handleError(Object exception, StackTrace? stackTrace) {
    // 忽略加载失败，指示器不绘制
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    if (configuration.size == null) return;

    _resolveImage(configuration);

    final Rect rect = offset & configuration.size!;
    final double dx = rect.left + (rect.width - imageSize.width) / 2;
    final double dy = rect.bottom - bottomPadding - imageSize.height;
    final Rect dstRect = Rect.fromLTWH(
      dx,
      dy,
      imageSize.width,
      imageSize.height,
    );

    final ImageInfo? info = _imageInfo;
    if (info == null) {
      return; // 图片尚未加载完成
    }

    final Size naturalSize = Size(
      info.image.width.toDouble(),
      info.image.height.toDouble(),
    );
    final Rect srcRect = Offset.zero & naturalSize;

    final Paint paint = Paint()..isAntiAlias = true;
    canvas.drawImageRect(info.image, srcRect, dstRect, paint);
  }

  @override
  void dispose() {
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = null;
    _imageInfo = null;
    super.dispose();
  }
}
