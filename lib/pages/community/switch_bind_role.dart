import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../components/game_character_binding_dialog.dart';
import '../../model/game_role.dart';
import '../../providers/role_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/log_util.dart';

class SwitchBindRoleDialog {
  /// 显示角色切换弹窗
  static void showCharacterSwitchDialog(BuildContext context, {
    Function? onCharacterSwitched,
    VoidCallback? onNavigateToBindCharacter,
  }) {
    final roleProvider = Provider.of<RoleProvider>(context, listen: false);
    
    // 转换角色数据格式
    final characters = roleProvider.boundRoles
        .map(
          (role) => GameCharacter(
            id: role.roleFavoriteId.toString(),
            name: role.drname,
            server: role.dsname,
            level: '',
            // 角色等级信息在V2接口中没有，暂时为空
            avatar: 'assets/images/avatar.png',
            // 使用默认头像
            isSelected: role.isPicked,
          ),
        )
        .toList();

    GameCharacterBindingDialog.show(
      context: context,
      characters: characters,
      onCharacterSelected: (selectedCharacter) {
        _handleCharacterSwitch(context, selectedCharacter, onCharacterSwitched);
      },
      onBindOtherCharacter: () {
        onNavigateToBindCharacter?.call();
      },
      onClose: () {
        // 弹窗关闭时不需要特殊处理
      },
    );
  }

  /// 处理角色切换
  static Future<void> _handleCharacterSwitch(
    BuildContext context, 
    GameCharacter selectedCharacter,
    Function? onCharacterSwitched,
  ) async {
    try {
      final roleProvider = Provider.of<RoleProvider>(context, listen: false);
      
      // 找到对应的角色数据
      final selectedRoleData = roleProvider.boundRoles.firstWhere(
        (role) => role.roleFavoriteId.toString() == selectedCharacter.id,
      );

      LogUtil.d('开始切换角色: ${selectedCharacter.name}');

      // 调用切换角色接口
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final uid = userProvider.currentUser?.muid ?? '';

      final success = await roleProvider.setDefaultRole(
        context,
        roleFavoriteId: selectedRoleData.roleFavoriteId.toString(),
        uid: uid,
        rolePid: selectedRoleData.rolePid.toString(),
        roleGid: selectedRoleData.roleGid.toString(),
        drid: selectedRoleData.drid.toString(),
        dsid: selectedRoleData.dsid,
        drname: selectedRoleData.drname,
        dsname: selectedRoleData.dsname,
      );

      if (success) {
        LogUtil.d('角色切换成功');

        if (context.mounted) {
          // 关闭弹窗
          Navigator.of(context).pop();

          // 显示成功提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已切换到角色：${selectedCharacter.name}'),
              backgroundColor: Colors.green,
            ),
          );

          // 调用回调函数通知父页面刷新
          onCharacterSwitched?.call();
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(roleProvider.error ?? '角色切换失败'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      LogUtil.e('角色切换异常: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('角色切换失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}